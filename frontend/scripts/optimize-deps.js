#!/usr/bin/env node

/**
 * 依赖优化脚本
 * 分析和移除未使用的依赖，优化包体积
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 开始分析依赖使用情况...\n');

// 读取package.json
const packageJsonPath = path.join(__dirname, '../package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// 可能重复或未使用的依赖
const potentiallyUnusedDeps = [
  '@ant-design/icons', // 可能与lucide-react重复
  '@phosphor-icons/react', // 可能与lucide-react重复
  'TagCloud', // 可能与react-icon-cloud重复
  'cheerio', // 可能只在构建时使用
  'csv-parse', // 可能未使用
  'favicon-stealer', // 可能未使用
  'prism-react-renderer', // 可能与react-syntax-highlighter重复
  '@mapbox/rehype-prism', // 可能与react-syntax-highlighter重复
  'remark-html', // 可能与next-mdx-remote重复
];

// 重复功能的依赖组
const duplicateGroups = {
  '图标库': [
    '@ant-design/icons',
    '@phosphor-icons/react', 
    'lucide-react',
    'react-icon-cloud'
  ],
  'Markdown处理': [
    '@mdx-js/loader',
    '@mdx-js/react',
    'next-mdx-remote',
    'react-markdown',
    'remark',
    'remark-gfm',
    'remark-html'
  ],
  '语法高亮': [
    '@mapbox/rehype-prism',
    'prism-react-renderer',
    'react-syntax-highlighter'
  ]
};

console.log('📦 当前依赖分析：');
console.log(`总依赖数量: ${Object.keys(packageJson.dependencies).length}`);
console.log(`开发依赖数量: ${Object.keys(packageJson.devDependencies || {}).length}\n`);

console.log('🔄 重复功能依赖组：');
Object.entries(duplicateGroups).forEach(([group, deps]) => {
  console.log(`\n${group}:`);
  deps.forEach(dep => {
    if (packageJson.dependencies[dep]) {
      console.log(`  ✓ ${dep} - ${packageJson.dependencies[dep]}`);
    }
  });
});

console.log('\n⚠️  可能未使用的依赖：');
potentiallyUnusedDeps.forEach(dep => {
  if (packageJson.dependencies[dep]) {
    console.log(`  • ${dep} - ${packageJson.dependencies[dep]}`);
  }
});

console.log('\n💡 优化建议：');
console.log('1. 统一使用 lucide-react 作为主要图标库');
console.log('2. 使用 next-mdx-remote 处理 MDX，移除其他 Markdown 库');
console.log('3. 使用 react-syntax-highlighter 进行语法高亮');
console.log('4. 运行 npm audit 检查安全漏洞');
console.log('5. 使用 webpack-bundle-analyzer 分析包体积');

console.log('\n🚀 要执行自动优化，请运行：');
console.log('npm run optimize:deps:auto');
