import rehypePrism from '@mapbox/rehype-prism'
import nextMDX from '@next/mdx'
import remarkGfm from 'remark-gfm'

/** @type {import('next').NextConfig} */
const nextConfig = {
  pageExtensions: ['js', 'jsx', 'ts', 'tsx', 'mdx'],

  // Tailscale 跨域配置
  allowedDevOrigins: [
    '**************:3001', // Tailscale IP
    'localhost:3001',
    '127.0.0.1:3001'
  ],

  // 启用图片优化
  images: {
    unoptimized: false, // 启用 Next.js 图片优化
    formats: ['image/webp', 'image/avif'], // 现代图片格式
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1年缓存
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'favicon.im'
      },
      {
        protocol: 'https',
        hostname: 'www.google.com'
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000'
      },
      {
        protocol: 'http',
        hostname: '**************',
        port: '8000'
      }
    ],
  },

  // 性能优化配置
  experimental: {
    optimizeCss: false, // 暂时禁用CSS优化以避免critters依赖问题
    optimizePackageImports: [
      'lucide-react', // 主要图标库
      'react-syntax-highlighter', // 语法高亮
      'react-markdown', // Markdown渲染
      'next-mdx-remote', // MDX处理
      'framer-motion', // 动画库
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-tabs'
    ], // 包导入优化 - 移除重复依赖
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    // 启用更多优化
    serverComponentsExternalPackages: [
      'sharp', // 图片处理
      'cheerio', // HTML解析
      'gray-matter' // Front matter解析
    ],
  },

  // 编译优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production', // 生产环境移除 console
  },

  // 输出配置
  output: 'standalone', // 优化部署体积

  // 压缩配置
  compress: true,

  // 静态优化
  trailingSlash: false,

  // Webpack 优化配置
  webpack: (config, { dev, isServer }) => {
    // 开发环境优化
    if (dev) {
      // 减少开发环境的模块解析时间
      config.resolve.alias = {
        ...config.resolve.alias,
        // 使用更快的图标库别名
        '@icons': 'lucide-react',
      }

      // 开发环境缓存优化
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: ['./next.config.mjs'],
        },
      }
    }

    // 生产环境优化
    if (!dev && !isServer) {
      // 代码分割优化
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: 25,
        maxAsyncRequests: 25,
        cacheGroups: {
          // React相关库
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 30,
          },
          // UI组件库单独打包
          ui: {
            test: /[\\/]node_modules[\\/](@headlessui|@radix-ui)[\\/]/,
            name: 'ui-libs',
            chunks: 'all',
            priority: 25,
          },
          // 图标库单独打包 - 只保留主要的
          icons: {
            test: /[\\/]node_modules[\\/](lucide-react)[\\/]/,
            name: 'icon-libs',
            chunks: 'all',
            priority: 20,
          },
          // Markdown处理库
          markdown: {
            test: /[\\/]node_modules[\\/](next-mdx-remote|react-markdown|remark|rehype)[\\/]/,
            name: 'markdown-libs',
            chunks: 'all',
            priority: 15,
          },
          // 第三方库
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
            minChunks: 2,
          },
          // 公共组件
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      }
    }

    return config
  },

  // 重定向优化
  async redirects() {
    return []
  },

  // Headers 优化
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, s-maxage=60, stale-while-revalidate=300',
          },
        ],
      },
    ]
  },
}

const withMDX = nextMDX({
  extension: /\.mdx?$/,
  options: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [rehypePrism],
  },
})

export default withMDX(nextConfig)
