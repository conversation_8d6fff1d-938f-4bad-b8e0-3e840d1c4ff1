"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/version-history/[id]/page",{

/***/ "(app-pages-browser)/./src/components/layout/VersionLayout.tsx":
/*!*************************************************!*\
  !*** ./src/components/layout/VersionLayout.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VersionLayout: function() { return /* binding */ VersionLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Container */ \"(app-pages-browser)/./src/components/layout/Container.tsx\");\n/* harmony import */ var _components_shared_Prose__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/shared/Prose */ \"(app-pages-browser)/./src/components/shared/Prose.tsx\");\n/* harmony import */ var _lib_formatDate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/formatDate */ \"(app-pages-browser)/./src/lib/formatDate.ts\");\n/* harmony import */ var _components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/blog/ResponsiveTableOfContents */ \"(app-pages-browser)/./src/components/blog/ResponsiveTableOfContents.tsx\");\n/* harmony import */ var _components_comment_CommentStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/comment/CommentStats */ \"(app-pages-browser)/./src/components/comment/CommentStats.tsx\");\n/* harmony import */ var _lib_colorSystem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/colorSystem */ \"(app-pages-browser)/./src/lib/colorSystem.ts\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ VersionLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction VersionLayout({ version, children }) {\n    _s();\n    let router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    let { previousPathname } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_app_providers__WEBPACK_IMPORTED_MODULE_3__.AppContext);\n    const [contentString, setContentString] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    // 提取内容用于目录生成\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        // 尝试多种方式提取内容\n        let extractedContent = \"\";\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children) && children.props?.content) {\n            extractedContent = children.props.content;\n        } else if (version.content) {\n            extractedContent = version.content;\n        }\n        setContentString(extractedContent);\n    }, [\n        children,\n        version\n    ]);\n    const getTags = (tagsString)=>{\n        if (!tagsString) return [];\n        return tagsString.split(\",\").map((tag)=>tag.trim()).filter(Boolean);\n    };\n    // 使用与博客页一致的标签样式\n    const getTagStyle = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_9__.tagStyleGenerator.getTagStyle(color, \"minimal\");\n    const getTagClasses = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_9__.tagStyleGenerator.getTagClasses(color, \"minimal\");\n    // 增强的标签悬停效果\n    const getEnhancedTagStyle = (color)=>{\n        const baseStyle = getTagStyle(color);\n        return {\n            ...baseStyle,\n            transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n            transform: \"perspective(100px) translateZ(0)\",\n            boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1)\"\n        };\n    };\n    const tags = getTags(version.tags);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_4__.Container, {\n            className: \"mt-16 lg:mt-32\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"xl:relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl\",\n                        children: [\n                            previousPathname && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>router.back(),\n                                    \"aria-label\": \"Go back to version history\",\n                                    className: \"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowLeftIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                className: \"animate-fade-in-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                        className: \"relative mb-16 p-10 rounded-3xl bg-gradient-to-r from-background/85 via-background/95 to-background/85 border border-border/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl hover:shadow-primary/10 transition-all duration-700 group/header overflow-hidden\",\n                                        style: {\n                                            transform: \"perspective(1000px) translateZ(0)\",\n                                            transformStyle: \"preserve-3d\",\n                                            boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent opacity-60\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-primary/30 rounded-tl-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-primary/30 rounded-tr-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `relative inline-flex items-center px-4 py-2 text-xs font-bold rounded-full hover:scale-105 hover:shadow-lg transition-all duration-300 cursor-default overflow-hidden ${version.is_major ? \"bg-gradient-to-r from-red-500/15 to-orange-500/15 border-2 border-red-500/30 text-red-700 dark:text-red-300 hover:shadow-red-500/25\" : \"bg-gradient-to-r from-blue-500/15 to-cyan-500/15 border-2 border-blue-500/30 text-blue-700 dark:text-blue-300 hover:shadow-blue-500/25\"}`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300 ${version.is_major ? \"bg-gradient-to-r from-red-500/20 to-orange-500/20\" : \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20\"}`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 130,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"relative z-10 tracking-wide\",\n                                                                        children: version.version\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            version.is_major && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"relative inline-flex items-center gap-1.5 px-4 py-2 text-xs font-bold bg-gradient-to-r from-orange-500/15 to-red-500/15 border-2 border-orange-500/30 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 cursor-default overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3 fill-current animate-pulse-soft relative z-10\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"relative z-10 tracking-wide\",\n                                                                        children: \"MAJOR RELEASE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md\",\n                                                        style: {\n                                                            transform: \"translateZ(20px)\",\n                                                            transformStyle: \"preserve-3d\",\n                                                            fontFamily: 'ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif'\n                                                        },\n                                                        children: version.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap items-center gap-8 text-sm text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-1.5 rounded-lg bg-primary/10 text-primary group-hover/meta:bg-primary/20 group-hover/meta:scale-110 transition-all duration-300\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                            lineNumber: 164,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                                        dateTime: version.release_date,\n                                                                        className: \"group-hover/meta:text-primary transition-colors duration-300 font-medium\",\n                                                                        children: (0,_lib_formatDate__WEBPACK_IMPORTED_MODULE_6__.formatDate)(version.release_date)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            version.author && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-500/10 text-blue-500 group-hover/meta:bg-blue-500/20 group-hover/meta:scale-110 transition-all duration-300\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                            lineNumber: 175,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"group-hover/meta:text-primary transition-colors duration-300 font-medium\",\n                                                                        children: [\n                                                                            \"Released by \",\n                                                                            version.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-1.5 rounded-lg bg-purple-500/10 text-purple-500 group-hover/meta:bg-purple-500/20 group-hover/meta:scale-110 transition-all duration-300\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"group-hover/meta:text-primary transition-colors duration-300 font-medium\",\n                                                                        children: \"Version Update\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_CommentStats__WEBPACK_IMPORTED_MODULE_8__.CommentStats, {\n                                                                path: `/versions/${version.id}`,\n                                                                showIcons: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mb-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide\",\n                                                                                children: \"ABSTRACT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                                lineNumber: 202,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-6 right-6 w-3 h-3 bg-primary/20 rounded-full blur-sm animate-pulse-soft\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-6 left-6 w-2 h-2 bg-secondary/30 rounded-full blur-sm animate-pulse-soft\",\n                                                                        style: {\n                                                                            animationDelay: \"1s\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative z-10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg sm:text-xl leading-relaxed text-muted-foreground group-hover/header:text-foreground transition-colors duration-300 font-medium\",\n                                                                            style: {\n                                                                                fontFamily: 'ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif',\n                                                                                fontStyle: \"italic\"\n                                                                            },\n                                                                            children: \"This version introduces new features, improvements, and bug fixes to enhance the overall user experience and system performance.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(getTagClasses(\"#10B981\"), \"hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden\"),\n                                                                style: getEnhancedTagStyle(\"#10B981\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm\",\n                                                                        style: {\n                                                                            backgroundColor: \"#10B981\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300\",\n                                                                        children: tag\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Prose__WEBPACK_IMPORTED_MODULE_5__.Prose, {\n                                        className: \"mt-8\",\n                                        \"data-mdx-content\": true,\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden 2xl:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_7__.ResponsiveTableOfContents, {\n                            content: contentString\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden xl:block 2xl:hidden fixed right-2 top-1/2 transform -translate-y-1/2 w-72 z-40 pointer-events-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_7__.ResponsiveTableOfContents, {\n                            content: contentString\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_7__.ResponsiveTableOfContents, {\n                            content: contentString\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(VersionLayout, \"qvBbIo1qI1KMkHAX2Fu/1Uc1qLc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = VersionLayout;\nfunction ArrowLeftIcon(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        \"aria-hidden\": \"true\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"m9.25 10.75-3.5-3.5 3.5-3.5\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/VersionLayout.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ArrowLeftIcon;\nvar _c, _c1;\n$RefreshReg$(_c, \"VersionLayout\");\n$RefreshReg$(_c1, \"ArrowLeftIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/VersionLayout.tsx\n"));

/***/ })

});