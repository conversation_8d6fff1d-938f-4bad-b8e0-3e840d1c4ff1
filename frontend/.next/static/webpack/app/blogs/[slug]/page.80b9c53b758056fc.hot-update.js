"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/comment/PageViewCount.tsx":
/*!**************************************************!*\
  !*** ./src/components/comment/PageViewCount.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageViewCount: function() { return /* binding */ PageViewCount; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _waline_client_pageview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @waline/client/pageview */ \"(app-pages-browser)/./node_modules/@waline/client/dist/pageview.js\");\n/* __next_internal_client_entry_do_not_use__ PageViewCount,default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PageViewCount({ path, className = \"\", showLabel = true, label = \"Views\", update = true }) {\n    _s();\n    const countRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const abortRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!countRef.current) return;\n        // 取消之前的请求\n        if (abortRef.current) {\n            abortRef.current();\n        }\n        // 设置data-path属性\n        if (path) {\n            countRef.current.setAttribute(\"data-path\", path);\n        }\n        // 启动浏览量统计\n        abortRef.current = (0,_waline_client_pageview__WEBPACK_IMPORTED_MODULE_2__.pageviewCount)({\n            serverURL: \"https://waline.jyaochen.cn\",\n            path: path || window.location.pathname,\n            selector: \".waline-pageview-count\",\n            update: update\n        });\n        return ()=>{\n            try {\n                if (abortRef.current) {\n                    abortRef.current();\n                }\n            } catch (error) {\n                // 忽略AbortError\n                if (error instanceof Error && error.name !== \"AbortError\") {\n                    console.warn(\"PageViewCount cleanup error:\", error);\n                }\n            }\n        };\n    }, [\n        path,\n        update\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: `inline-flex items-center gap-1 ${className}`,\n        children: [\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm text-muted-foreground\",\n                children: [\n                    label,\n                    \":\"\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/PageViewCount.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                ref: countRef,\n                className: \"waline-pageview-count text-sm font-medium\",\n                \"data-path\": path || window.location.pathname,\n                children: \"0\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/PageViewCount.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/PageViewCount.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(PageViewCount, \"35EqyXS5qVFfAoNe2KhWV79z9RA=\");\n_c = PageViewCount;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PageViewCount);\nvar _c;\n$RefreshReg$(_c, \"PageViewCount\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comment/PageViewCount.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/BlogLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/BlogLayout.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogLayout: function() { return /* binding */ BlogLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Container */ \"(app-pages-browser)/./src/components/layout/Container.tsx\");\n/* harmony import */ var _components_shared_Prose__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/Prose */ \"(app-pages-browser)/./src/components/shared/Prose.tsx\");\n/* harmony import */ var _components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/blog/TableOfContents */ \"(app-pages-browser)/./src/components/blog/TableOfContents.tsx\");\n/* harmony import */ var _components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/blog/ResponsiveTableOfContents */ \"(app-pages-browser)/./src/components/blog/ResponsiveTableOfContents.tsx\");\n/* harmony import */ var _components_comment_LazyWalineComment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/comment/LazyWalineComment */ \"(app-pages-browser)/./src/components/comment/LazyWalineComment.tsx\");\n/* harmony import */ var _components_comment_CommentStats__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/comment/CommentStats */ \"(app-pages-browser)/./src/components/comment/CommentStats.tsx\");\n/* harmony import */ var _components_comment_PageViewCount__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/comment/PageViewCount */ \"(app-pages-browser)/./src/components/comment/PageViewCount.tsx\");\n/* harmony import */ var _lib_formatDate__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/formatDate */ \"(app-pages-browser)/./src/lib/formatDate.ts\");\n/* harmony import */ var _lib_colorSystem__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/colorSystem */ \"(app-pages-browser)/./src/lib/colorSystem.ts\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ BlogLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ArrowLeftIcon(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        \"aria-hidden\": \"true\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_c = ArrowLeftIcon;\nfunction BlogLayout({ blog, children }) {\n    _s();\n    let router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    let { previousPathname } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_app_providers__WEBPACK_IMPORTED_MODULE_4__.AppContext);\n    const [contentString, setContentString] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    // 提取内容用于目录生成\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children) && children.props?.content) {\n            setContentString(children.props.content);\n        }\n    }, [\n        children\n    ]);\n    // 监听DOM变化，确保目录能够正确提取\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const timer = setTimeout(()=>{\n            // 触发目录组件重新扫描DOM\n            const event = new CustomEvent(\"tocRefresh\");\n            window.dispatchEvent(event);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, [\n        contentString\n    ]);\n    // 使用与列表页一致的标签样式\n    const getTagStyle = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_13__.tagStyleGenerator.getTagStyle(color, \"minimal\");\n    const getTagClasses = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_13__.tagStyleGenerator.getTagClasses(color, \"minimal\");\n    // 增强的标签悬停效果\n    const getEnhancedTagStyle = (color)=>{\n        const baseStyle = getTagStyle(color);\n        return {\n            ...baseStyle,\n            transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n            transform: \"perspective(100px) translateZ(0)\",\n            transformStyle: \"preserve-3d\"\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__.ReadingProgress, {}, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_5__.Container, {\n                className: \"mt-8 lg:mt-16 mb-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block lg:hidden absolute right-0 top-0 w-72 z-40 pointer-events-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__.TableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl min-h-0\",\n                            children: [\n                                previousPathname && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.back(),\n                                        \"aria-label\": \"Go back to blogs\",\n                                        className: \"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowLeftIcon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"animate-fade-in-up\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                            className: \"relative mb-12 p-8 rounded-3xl bg-gradient-to-r from-background/80 via-background/90 to-background/80 border border-border/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 group/header overflow-hidden\",\n                                            style: {\n                                                transform: \"perspective(1000px) translateZ(0)\",\n                                                transformStyle: \"preserve-3d\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                blog.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 transition-all duration-200 cursor-default\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3 fill-current animate-pulse-soft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 128,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Featured\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                blog.is_major_change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 transition-all duration-200 cursor-default\",\n                                                                    children: \"Major Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md\",\n                                                            style: {\n                                                                transform: \"translateZ(20px)\",\n                                                                transformStyle: \"preserve-3d\"\n                                                            },\n                                                            children: blog.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        blog.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            style: {\n                                                                transform: \"translateZ(10px)\",\n                                                                transformStyle: \"preserve-3d\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 mb-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 163,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide\",\n                                                                                    children: \"PRELUDE\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 164,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 168,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-bl from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-gradient-to-tr from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700\",\n                                                                            style: {\n                                                                                transitionDelay: \"0.2s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute left-4 top-6 w-1 h-16 bg-gradient-to-b from-primary/60 to-primary/20 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative z-10 pl-6\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-base sm:text-lg leading-relaxed text-slate-700 dark:text-slate-300 group-hover/header:text-slate-900 dark:group-hover/header:text-slate-100 transition-colors duration-300 font-light tracking-wide\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-primary/70 font-serif leading-none mr-1\",\n                                                                                        children: '\"'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 186,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"italic\",\n                                                                                        children: blog.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 187,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-primary/70 font-serif leading-none ml-1\",\n                                                                                        children: '\"'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 188,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 185,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex items-center gap-1 opacity-60 group-hover/header:opacity-100 transition-opacity duration-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-primary/40 rounded-full animate-pulse-soft\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 194,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1.5 h-1.5 bg-primary/30 rounded-full animate-pulse-soft\",\n                                                                                    style: {\n                                                                                        animationDelay: \"0.3s\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 195,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1 h-1 bg-primary/20 rounded-full animate-pulse-soft\",\n                                                                                    style: {\n                                                                                        animationDelay: \"0.6s\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 196,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 rounded-3xl border border-white/20 dark:border-white/10 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap items-center gap-6 text-sm text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 209,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                                            dateTime: blog.display_date,\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: (0,_lib_formatDate__WEBPACK_IMPORTED_MODULE_12__.formatDate)(blog.display_date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 217,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: blog.author\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_PageViewCount__WEBPACK_IMPORTED_MODULE_11__.PageViewCount, {\n                                                                            path: `/blogs/${blog.slug}`,\n                                                                            showLabel: false,\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                blog.likes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: [\n                                                                                blog.likes,\n                                                                                \" likes\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_CommentStats__WEBPACK_IMPORTED_MODULE_10__.CommentStats, {\n                                                                    path: `/blogs/${blog.slug}`,\n                                                                    showIcons: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        blog.tags && blog.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: blog.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    href: `/blogs?tag=${tag.slug || tag.id}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(getTagClasses(tag.color), \"hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden\"),\n                                                                        style: getEnhancedTagStyle(tag.color),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm\",\n                                                                                style: {\n                                                                                    backgroundColor: tag.color || \"hsl(var(--primary))\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300\",\n                                                                                children: tag.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, tag.id, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"article-content\",\n                                            \"data-mdx-content\": true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Prose__WEBPACK_IMPORTED_MODULE_6__.Prose, {\n                                                className: \"mt-8 mb-0\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                            className: \"mt-12 mb-12\",\n                                            \"data-comment-section\": true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-4 backdrop-blur-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"p-2 rounded-xl bg-primary/10 text-primary\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase\",\n                                                                                children: \"Discussion\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3\",\n                                                                        children: \"Join the Conversation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed\",\n                                                                        children: \"Your thoughts and insights are valuable. Share your perspective and connect with others.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_LazyWalineComment__WEBPACK_IMPORTED_MODULE_9__.LazyWalineComment, {\n                                                                path: `/blogs/${blog.slug}`,\n                                                                title: blog.title,\n                                                                className: \"max-w-5xl mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__.ResponsiveTableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__.ResponsiveTableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogLayout, \"/33IpqIXwv7s7HEf4TRQcHGHndA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = BlogLayout;\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowLeftIcon\");\n$RefreshReg$(_c1, \"BlogLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/BlogLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@waline/client/dist/pageview.js":
/*!******************************************************!*\
  !*** ./node_modules/@waline/client/dist/pageview.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pageviewCount: function() { return /* binding */ S; },\n/* harmony export */   version: function() { return /* binding */ v; }\n/* harmony export */ });\nconst v=\"3.6.0\",$={\"Content-Type\":\"application/json\"},h=e=>`${e.replace(/\\/?$/,\"/\")}api/`,u=(e,t=\"\")=>{if(typeof e==\"object\"&&e.errno)throw new TypeError(`${t} failed with ${e.errno}: ${e.errmsg}`);return e},f=({serverURL:e,lang:t,paths:r,type:o,signal:a})=>fetch(`${h(e)}article?path=${encodeURIComponent(r.join(\",\"))}&type=${encodeURIComponent(o.join(\",\"))}&lang=${t}`,{signal:a}).then(n=>n.json()).then(n=>u(n,\"Get counter\").data),R=({serverURL:e,lang:t,path:r,type:o,action:a})=>fetch(`${h(e)}article?lang=${t}`,{method:\"POST\",headers:$,body:JSON.stringify({path:r,type:o,action:a})}).then(n=>n.json()).then(n=>u(n,\"Update counter\").data),U=({serverURL:e,lang:t,paths:r,signal:o})=>f({serverURL:e,lang:t,paths:r,type:[\"time\"],signal:o}),w=e=>R({...e,type:\"time\",action:\"inc\"}),L=(e=\"\")=>e.replace(/\\/$/u,\"\"),b=e=>/^(https?:)?\\/\\//.test(e),d=e=>{const t=L(e);return b(t)?t:`https://${t}`},j=e=>{e.name!==\"AbortError\"&&console.error(e.message)},m=e=>{const{path:t}=e.dataset;return t!=null&&t.length?t:null},y=(e,t)=>{t.forEach((r,o)=>{const a=e[o].time;typeof a==\"number\"&&(r.innerText=a.toString())})},S=({serverURL:e,path:t=window.location.pathname,selector:r=\".waline-pageview-count\",update:o=!0,lang:a=navigator.language})=>{const n=new AbortController,i=Array.from(document.querySelectorAll(r)),p=l=>{const s=m(l);return s!==null&&t!==s},g=l=>U({serverURL:d(e),paths:l.map(s=>m(s)??t),lang:a,signal:n.signal}).then(s=>y(s,l)).catch(j);if(o){const l=i.filter(c=>!p(c)),s=i.filter(p);w({serverURL:d(e),path:t,lang:a}).then(c=>y(c,l)),s.length&&g(s)}else g(i);return n.abort.bind(n)};\n//# sourceMappingURL=pageview.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@waline/client/dist/pageview.js\n"));

/***/ })

});