"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/layout/BlogLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/BlogLayout.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogLayout: function() { return /* binding */ BlogLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Container */ \"(app-pages-browser)/./src/components/layout/Container.tsx\");\n/* harmony import */ var _components_shared_Prose__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/Prose */ \"(app-pages-browser)/./src/components/shared/Prose.tsx\");\n/* harmony import */ var _components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/blog/TableOfContents */ \"(app-pages-browser)/./src/components/blog/TableOfContents.tsx\");\n/* harmony import */ var _components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/blog/ResponsiveTableOfContents */ \"(app-pages-browser)/./src/components/blog/ResponsiveTableOfContents.tsx\");\n/* harmony import */ var _components_comment_LazyWalineComment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/comment/LazyWalineComment */ \"(app-pages-browser)/./src/components/comment/LazyWalineComment.tsx\");\n/* harmony import */ var _components_comment_PageViewCount__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/comment/PageViewCount */ \"(app-pages-browser)/./src/components/comment/PageViewCount.tsx\");\n/* harmony import */ var _lib_formatDate__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/formatDate */ \"(app-pages-browser)/./src/lib/formatDate.ts\");\n/* harmony import */ var _lib_colorSystem__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/colorSystem */ \"(app-pages-browser)/./src/lib/colorSystem.ts\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ BlogLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ArrowLeftIcon(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        \"aria-hidden\": \"true\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = ArrowLeftIcon;\nfunction BlogLayout({ blog, children }) {\n    _s();\n    let router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    let { previousPathname } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_app_providers__WEBPACK_IMPORTED_MODULE_4__.AppContext);\n    const [contentString, setContentString] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    // 提取内容用于目录生成\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children) && children.props?.content) {\n            setContentString(children.props.content);\n        }\n    }, [\n        children\n    ]);\n    // 监听DOM变化，确保目录能够正确提取\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const timer = setTimeout(()=>{\n            // 触发目录组件重新扫描DOM\n            const event = new CustomEvent(\"tocRefresh\");\n            window.dispatchEvent(event);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, [\n        contentString\n    ]);\n    // 使用与列表页一致的标签样式\n    const getTagStyle = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_12__.tagStyleGenerator.getTagStyle(color, \"minimal\");\n    const getTagClasses = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_12__.tagStyleGenerator.getTagClasses(color, \"minimal\");\n    // 增强的标签悬停效果\n    const getEnhancedTagStyle = (color)=>{\n        const baseStyle = getTagStyle(color);\n        return {\n            ...baseStyle,\n            transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n            transform: \"perspective(100px) translateZ(0)\",\n            transformStyle: \"preserve-3d\"\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__.ReadingProgress, {}, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_5__.Container, {\n                className: \"mt-8 lg:mt-16 mb-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block lg:hidden absolute right-0 top-0 w-72 z-40 pointer-events-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__.TableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl min-h-0\",\n                            children: [\n                                previousPathname && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.back(),\n                                        \"aria-label\": \"Go back to blogs\",\n                                        className: \"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowLeftIcon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"animate-fade-in-up\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                            className: \"relative mb-12 p-8 rounded-3xl bg-gradient-to-r from-background/80 via-background/90 to-background/80 border border-border/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 group/header overflow-hidden\",\n                                            style: {\n                                                transform: \"perspective(1000px) translateZ(0)\",\n                                                transformStyle: \"preserve-3d\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                blog.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 transition-all duration-200 cursor-default\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-3 h-3 fill-current animate-pulse-soft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 129,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Featured\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                blog.is_major_change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 transition-all duration-200 cursor-default\",\n                                                                    children: \"Major Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md\",\n                                                            style: {\n                                                                transform: \"translateZ(20px)\",\n                                                                transformStyle: \"preserve-3d\"\n                                                            },\n                                                            children: blog.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        blog.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            style: {\n                                                                transform: \"translateZ(10px)\",\n                                                                transformStyle: \"preserve-3d\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 mb-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 164,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide\",\n                                                                                    children: \"PRELUDE\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 165,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 163,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 169,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 175,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-bl from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-gradient-to-tr from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700\",\n                                                                            style: {\n                                                                                transitionDelay: \"0.2s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 179,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute left-4 top-6 w-1 h-16 bg-gradient-to-b from-primary/60 to-primary/20 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative z-10 pl-6\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-base sm:text-lg leading-relaxed text-slate-700 dark:text-slate-300 group-hover/header:text-slate-900 dark:group-hover/header:text-slate-100 transition-colors duration-300 font-light tracking-wide\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-primary/70 font-serif leading-none mr-1\",\n                                                                                        children: '\"'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 187,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"italic\",\n                                                                                        children: blog.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 188,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-primary/70 font-serif leading-none ml-1\",\n                                                                                        children: '\"'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 189,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 186,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 185,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex items-center gap-1 opacity-60 group-hover/header:opacity-100 transition-opacity duration-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-primary/40 rounded-full animate-pulse-soft\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 195,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1.5 h-1.5 bg-primary/30 rounded-full animate-pulse-soft\",\n                                                                                    style: {\n                                                                                        animationDelay: \"0.3s\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 196,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1 h-1 bg-primary/20 rounded-full animate-pulse-soft\",\n                                                                                    style: {\n                                                                                        animationDelay: \"0.6s\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 197,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 rounded-3xl border border-white/20 dark:border-white/10 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap items-center gap-6 text-sm text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                                            dateTime: blog.display_date,\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: (0,_lib_formatDate__WEBPACK_IMPORTED_MODULE_11__.formatDate)(blog.display_date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 211,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: blog.author\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_PageViewCount__WEBPACK_IMPORTED_MODULE_10__.PageViewCount, {\n                                                                            path: `/blogs/${blog.slug}`,\n                                                                            showLabel: false,\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 225,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CommentStats, {\n                                                                    path: `/blogs/${blog.slug}`,\n                                                                    showIcons: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        blog.tags && blog.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: blog.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    href: `/blogs?tag=${tag.slug || tag.id}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(getTagClasses(tag.color), \"hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden\"),\n                                                                        style: getEnhancedTagStyle(tag.color),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm\",\n                                                                                style: {\n                                                                                    backgroundColor: tag.color || \"hsl(var(--primary))\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 249,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300\",\n                                                                                children: tag.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, tag.id, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"article-content\",\n                                            \"data-mdx-content\": true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Prose__WEBPACK_IMPORTED_MODULE_6__.Prose, {\n                                                className: \"mt-8 mb-0\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                            className: \"mt-12 mb-12\",\n                                            \"data-comment-section\": true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-4 backdrop-blur-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"p-2 rounded-xl bg-primary/10 text-primary\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 290,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 289,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase\",\n                                                                                children: \"Discussion\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3\",\n                                                                        children: \"Join the Conversation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed\",\n                                                                        children: \"Your thoughts and insights are valuable. Share your perspective and connect with others.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_LazyWalineComment__WEBPACK_IMPORTED_MODULE_9__.LazyWalineComment, {\n                                                                path: `/blogs/${blog.slug}`,\n                                                                title: blog.title,\n                                                                className: \"max-w-5xl mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__.ResponsiveTableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__.ResponsiveTableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogLayout, \"/33IpqIXwv7s7HEf4TRQcHGHndA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = BlogLayout;\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowLeftIcon\");\n$RefreshReg$(_c1, \"BlogLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/BlogLayout.tsx\n"));

/***/ })

});