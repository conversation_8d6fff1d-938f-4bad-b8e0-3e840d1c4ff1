import { type Metadata } from 'next'

import { Providers } from '@/app/providers'
import { Layout } from '@/components/layout/Layout'
import { Analytics } from "@/components/analytics/analytics";
import { ThemeInitializer } from '@/components/theme/ThemeInitializer'
import { PerformanceMonitor, PerformanceDebugger } from '@/components/performance/PerformanceMonitor'
import ErrorBoundary from '@/components/ErrorBoundary'
import { introduction } from '@/config/infoConfig'
import { generatePageMetadata } from '@/lib/metadata'
import '@/styles/tailwind.css'

// 动态生成metadata
export async function generateMetadata(): Promise<Metadata> {
  try {
    // 从SEO系统获取配置
    const seoMetadata = await generatePageMetadata('homepage');

    // 直接使用SEO配置，不再依赖页面标题配置
    return {
      ...seoMetadata,
      title: {
        template: '%s',
        default: seoMetadata.title as string || 'Jingyao Chen - International Red Dot Award Designer & Full-Stack Developer',
      },
      alternates: {
        ...seoMetadata.alternates,
        types: {
          'application/rss+xml': `${process.env.NEXT_PUBLIC_SITE_URL}/feed`,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    // 降级到默认配置
    return {
      title: {
        template: '%s - Jay-Yao',
        default: 'Jay-Yao - Master\'s candidate in Information Science',
      },
      description: introduction,
      alternates: {
        types: {
          'application/rss+xml': `${process.env.NEXT_PUBLIC_SITE_URL}/feed`,
        },
      },
    };
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full antialiased font-wenkai" suppressHydrationWarning>
      <head>
        <link
          rel="preload"
          href="/fonts/lxgw-wenkai/LXGWWenKai-Regular.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/lxgw-wenkai/LXGWWenKai-Light.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/lxgw-wenkai/LXGWWenKai-Medium.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
      </head>
      <body className="flex h-full font-wenkai">
        <ErrorBoundary>
          <Providers>
            <ThemeInitializer />
            <div className="flex w-full">
              <Layout>{children}</Layout>
            </div>
            <Analytics />
            {/* 只在生产环境启用性能监控，开发环境仅启用调试器 */}
            {process.env.NODE_ENV === 'production' ? (
              <PerformanceMonitor />
            ) : (
              process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_DEBUG === 'true' && <PerformanceDebugger />
            )}
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  )
}
