'use client'

import React, { useState, useRef, useCallback } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { Album } from '@/utils/galleryUtils'
import { Folder, Image as ImageIcon, Calendar, ArrowRight, Play } from 'lucide-react'
import { cn } from '@/utils/cn'
import { galleryTheme, galleryCardStyles } from '@/lib/colorSystem'
import { useMagneticEffect, useTiltEffect } from '@/hooks/useGalleryInteractions'
import { FadeInUp, ScaleIn, GlowEffect, RippleEffect } from '@/components/gallery/GalleryAnimations'

type EnhancedAlbumsProps = {
  albums: Album[]
}

export function EnhancedAlbums({ albums }: EnhancedAlbumsProps) {
  const router = useRouter()

  // 处理相册点击
  const handleAlbumClick = (album: Album) => {
    // Album clicked
    // 统一跳转到相册详情页
    router.push(`/gallery/album/${album.id}`)
  }

  // 获取封面图片URL
  const getCoverImageUrl = (album: Album): string => {
    const baseUrl = 'http://**************:8000'

    if (!album.cover_image) return '/images/placeholder.svg'

    // 如果 cover_image 是对象，获取其 URL
    let url: string
    if (typeof album.cover_image === 'object' && album.cover_image !== null) {
      url = album.cover_image.thumbnail_url || album.cover_image.url
    } else {
      url = album.cover_image as string
    }

    if (!url) return '/images/placeholder.svg'

    if (url.startsWith('http://') || url.startsWith('https://')) return url
    if (!url.startsWith('/')) url = '/' + url
    return `${baseUrl}${url}`
  }

  // 格式化日期
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short'
      })
    } catch {
      return dateString
    }
  }

  // Album卡片组件 - 优化悬停效果，减少重新渲染
  const AlbumCard = React.memo(({ album, index }: { album: Album, index: number }) => {
    const magneticRef = useMagneticEffect(0.15) as React.RefObject<HTMLDivElement>
    const tiltRef = useTiltEffect(8) as React.RefObject<HTMLDivElement>
    const [imageLoaded, setImageLoaded] = useState(false)

    // 优化悬停效果，防止内容被截断
    const handleMouseEnter = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
      const card = e.currentTarget.querySelector('.album-card') as HTMLElement
      const container = e.currentTarget
      if (card && container) {
        // 减少位移，避免内容被截断
        card.style.transform = 'translateY(-8px) scale(1.02)'
        card.style.boxShadow = galleryTheme.shadows.cardHover
        // 给容器添加更大的z-index，确保悬停时在最上层
        container.style.zIndex = '10'
      }
    }, [])

    const handleMouseLeave = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
      const card = e.currentTarget.querySelector('.album-card') as HTMLElement
      const container = e.currentTarget
      if (card && container) {
        card.style.transform = 'translateY(0) scale(1)'
        card.style.boxShadow = galleryCardStyles.getAlbumCardStyle().boxShadow || ''
        // 恢复z-index
        container.style.zIndex = '1'
      }
    }, [])

    return (
      <FadeInUp delay={index * 150}>
        <RippleEffect>
          <div
            ref={magneticRef}
            className="group relative cursor-pointer"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              // Card clicked, calling handleAlbumClick
              handleAlbumClick(album)
            }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {/* 背景光晕 */}
            <div 
              className="absolute -inset-2 rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl"
              style={{ background: galleryTheme.gradients.glow }}
            />

            {/* 主卡片 - 优化高度和动画 */}
            <div
              ref={tiltRef}
              className="album-card relative rounded-2xl transition-all duration-500 transform-gpu h-[420px] group"
              style={{
                ...galleryCardStyles.getAlbumCardStyle(),
                overflow: 'visible', // 允许动画时内容可见
              }}
            >
              {/* 封面图片区域 */}
              <div className="relative h-64 overflow-hidden rounded-t-2xl">
                {/* 背景渐变 */}
                <div 
                  className="absolute inset-0 z-10"
                  style={{ background: galleryTheme.gradients.overlay }}
                />

                {/* 封面图片 */}
                <Image
                  src={getCoverImageUrl(album)}
                  alt={album.title}
                  fill
                  className={cn(
                    "object-cover transition-all duration-700 group-hover:scale-110",
                    imageLoaded ? "opacity-100" : "opacity-0"
                  )}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  onLoad={() => setImageLoaded(true)}
                />

                {/* 加载占位符 */}
                {!imageLoaded && (
                  <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse flex items-center justify-center">
                    <ImageIcon className="w-12 h-12 text-gray-400" />
                  </div>
                )}

                {/* 播放按钮 */}
                <div className="absolute inset-0 z-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <GlowEffect intensity="intense">
                    <div 
                      className="w-16 h-16 rounded-full flex items-center justify-center text-white shadow-2xl transition-transform duration-300 group-hover:scale-110"
                      style={{ backgroundColor: galleryTheme.primary.main }}
                    >
                      <Play className="w-6 h-6 ml-1" fill="currentColor" />
                    </div>
                  </GlowEffect>
                </div>

                {/* 图片数量标签 */}
                <div className="absolute top-4 right-4 z-20">
                  <div 
                    className="px-3 py-1 rounded-full text-white text-sm font-medium backdrop-blur-sm"
                    style={{ backgroundColor: `${galleryTheme.primary.main}CC` }}
                  >
                    <ImageIcon className="w-4 h-4 inline mr-1" />
                    {album.images?.length || 0}
                  </div>
                </div>
              </div>

              {/* 内容区域 - 优化布局 */}
              <div className="relative z-10 p-6 flex flex-col h-40 bg-white dark:bg-gray-800 rounded-b-2xl">
                {/* 标题 */}
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-1">
                  {album.title}
                </h3>

                {/* 描述 - 固定高度区域 */}
                <div className="h-10 mb-3">
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-2">
                    {album.description || '暂无描述'}
                  </p>
                </div>

                {/* 分类标签 - 移到内容区域内 */}
                <div className="mb-3">
                  {album.category && (
                    <span
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                      style={{
                        backgroundColor: `${galleryTheme.primary.main}20`,
                        color: galleryTheme.primary.main,
                        border: `1px solid ${galleryTheme.primary.main}30`
                      }}
                    >
                      <Folder className="w-3 h-3 mr-1" />
                      {typeof album.category === 'string' ? album.category : (album.category as any)?.name || album.category}
                    </span>
                  )}
                </div>

                {/* 元信息 - 固定在底部 */}
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mt-auto">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(album.created_at)}</span>
                  </div>

                  {/* 查看按钮 */}
                  <div className="flex items-center gap-1 text-primary font-medium group-hover:gap-2 transition-all duration-300">
                    <span>View Album</span>
                    <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </div>
                </div>
              </div>

              {/* 悬停时的装饰线条 - 使用CSS悬停 */}
              <div
                className="absolute bottom-0 left-0 h-1 bg-gradient-to-r transition-all duration-500 transform origin-left scale-x-0 group-hover:scale-x-100"
                style={{
                  background: galleryTheme.gradients.primary,
                  width: '100%'
                }}
              />
            </div>
          </div>
        </RippleEffect>
      </FadeInUp>
    )
  })

  // 统计信息组件
  const StatsSection = () => {
    const totalImages = albums.reduce((sum, album) => sum + (album.images?.length || 0), 0)
    const totalAlbums = albums.length

    return (
      <FadeInUp>
        <div className="mb-8 p-6 rounded-2xl border" style={galleryCardStyles.getAlbumCardStyle()}>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div 
                className="text-3xl font-bold mb-1"
                style={{ color: galleryTheme.primary.main }}
              >
                {totalAlbums}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Albums</div>
            </div>
            
            <div className="text-center">
              <div 
                className="text-3xl font-bold mb-1"
                style={{ color: galleryTheme.primary.main }}
              >
                {totalImages}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Photos</div>
            </div>
            
            <div className="text-center">
              <div 
                className="text-3xl font-bold mb-1"
                style={{ color: galleryTheme.primary.main }}
              >
                {Math.round(totalImages / totalAlbums) || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Avg per Album</div>
            </div>
            
            <div className="text-center">
              <div 
                className="text-3xl font-bold mb-1"
                style={{ color: galleryTheme.primary.main }}
              >
                {new Date().getFullYear() - Math.min(...albums.map(a => new Date(a.created_at).getFullYear()))}+
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Years</div>
            </div>
          </div>
        </div>
      </FadeInUp>
    )
  }

  if (albums.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
          <Folder className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No albums yet</h3>
        <p className="text-gray-500 dark:text-gray-400">Create your first album to get started</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* 统计信息 */}
      <StatsSection />

      {/* Albums网格 - 优化间距，给悬停动画留出空间 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
        {albums.map((album, index) => (
          <div key={album.id} className="relative" style={{ paddingTop: '12px', paddingBottom: '12px' }}>
            <AlbumCard album={album} index={index} />
          </div>
        ))}
      </div>
    </div>
  )
}
