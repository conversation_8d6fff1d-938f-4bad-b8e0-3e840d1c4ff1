'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Album, AlbumData, TimelineEntry, ImageData } from '@/utils/galleryUtils'
import { Calendar, Folder, Grid3X3, LayoutGrid, Clock, Image as ImageIcon, Sparkles } from 'lucide-react'
import { cn } from '@/utils/cn'
import { galleryTheme } from '@/lib/colorSystem'
import { EnhancedTimeline } from './EnhancedTimeline'
import { EnhancedAlbums } from './EnhancedAlbums'
import { EnhancedGrid } from './EnhancedGrid'
import { EnhancedSearch } from './EnhancedSearch'
import { FadeInUp, ScaleIn, SlideIn, GlowEffect } from './GalleryAnimations'
import { API_BASE_URL } from '@/lib/api'

type ViewType = 'timeline' | 'albums' | 'grid'

interface EnhancedGalleryContainerProps {
  albums?: AlbumData[]
  timelineEntries?: TimelineEntry[]
  initialView?: ViewType
  highlightedEntryId?: string
  highlightedAlbumId?: string
  galleryTitle?: string
  galleryDescription?: string
}

interface Category {
  id: number
  name: string
  color: string
  icon?: string
  image_count: number
}

export function EnhancedGalleryContainer({
  albums = [],
  timelineEntries = [],
  initialView = 'timeline',
  highlightedEntryId,
  highlightedAlbumId,
  galleryTitle = "Photo Gallery",
  galleryDescription = "Explore my photography collection through different perspectives - timeline memories, organized albums, or browse all photos in a grid."
}: EnhancedGalleryContainerProps) {

  // 转换 AlbumData 为 Album 格式
  const convertAlbumDataToAlbum = (albumData: AlbumData): Album => {
    return {
      id: albumData.id,
      title: albumData.title,
      description: albumData.description,
      cover_image: albumData.coverImage ? {
        id: parseInt(albumData.coverImage.id) || 0,
        url: albumData.coverImage.url,
        thumbnail_url: albumData.coverImage.thumbnail_url,
        display_name: albumData.coverImage.alt,
        original_filename: albumData.coverImage.alt || 'image',
        width: albumData.coverImage.width,
        height: albumData.coverImage.height,
      } : null,
      images: albumData.images,
      date: albumData.date,
      location: albumData.location,
      created_at: albumData.date,
      updated_at: albumData.date,
      slug: albumData.slug, // 添加slug字段
    }
  }

  const convertedAlbums = albums.map(convertAlbumDataToAlbum)
  const [activeView, setActiveView] = useState<ViewType>(initialView)
  const [images, setImages] = useState<ImageData[]>([])
  const [filteredImages, setFilteredImages] = useState<ImageData[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)

  // 从图片中提取可用标签
  const availableTags = React.useMemo(() => {
    const tagCounts = new Map<string, number>()
    
    images.forEach(image => {
      image.tags?.forEach(tag => {
        const tagName = typeof tag === 'string' ? tag : (tag as any).name || tag
        tagCounts.set(tagName, (tagCounts.get(tagName) || 0) + 1)
      })
    })

    return Array.from(tagCounts.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
  }, [images])

  // 从props中提取所有图片数据
  const loadImages = useCallback(() => {
    setLoading(true)
    const baseUrl = API_BASE_URL.replace('/api', '')

    // 处理图片URL，确保包含完整的服务器地址
    const processImageUrl = (url: string) => {
      if (!url) return '/images/placeholder.svg'
      if (url.startsWith('http://') || url.startsWith('https://')) return url
      if (!url.startsWith('/')) url = '/' + url
      return `${baseUrl}${url}`
    }

    // 从相册中提取所有图片
    const albumImages = albums.flatMap(album =>
      album.images.map(image => ({
        id: image.id,
        url: processImageUrl(image.url),
        thumbnail_url: processImageUrl(image.thumbnail_url || image.url),
        webp_url: processImageUrl(image.webp_url || image.url),
        thumbnail_webp_url: processImageUrl(image.thumbnail_webp_url || image.thumbnail_url || image.url),
        alt: image.alt,
        width: image.width,
        height: image.height,
        date: image.date,
        category: undefined, // 相册图片暂时没有分类
        tags: image.tags || [],
        created_at: image.date
      }))
    )

    // 从时间线中提取所有图片
    const timelineImages = timelineEntries.flatMap(entry =>
      entry.images.map(image => ({
        id: image.id,
        url: processImageUrl(image.url),
        thumbnail_url: processImageUrl(image.thumbnail_url || image.url),
        webp_url: processImageUrl(image.webp_url || image.url),
        thumbnail_webp_url: processImageUrl(image.thumbnail_webp_url || image.thumbnail_url || image.url),
        alt: image.alt,
        width: image.width,
        height: image.height,
        date: image.date,
        category: undefined,
        tags: image.tags || [],
        created_at: image.date
      }))
    )

    // 合并所有图片并去重
    const allImages = [...albumImages, ...timelineImages]
    const uniqueImages = allImages.filter((image, index, self) =>
      index === self.findIndex(img => img.id === image.id)
    )

    setImages(uniqueImages)
    setFilteredImages(uniqueImages)
    setLoading(false)
  }, [albums, timelineEntries])

  // 初始化加载
  useEffect(() => {
    loadImages()
  }, [loadImages])

  // 过滤图片
  useEffect(() => {
    let filtered = images

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(image =>
        image.alt?.toLowerCase().includes(query) ||
        image.tags?.some(tag => {
          const tagName = typeof tag === 'string' ? tag : (tag as any).name || tag
          return tagName.toLowerCase().includes(query)
        })
      )
    }

    // 分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(image => (image as any).category?.id === selectedCategory)
    }

    // 标签过滤
    if (selectedTags.length > 0) {
      filtered = filtered.filter(image =>
        selectedTags.every(selectedTag =>
          image.tags?.some(tag => {
            const tagName = typeof tag === 'string' ? tag : (tag as any).name || tag
            return tagName === selectedTag
          })
        )
      )
    }

    setFilteredImages(filtered)
  }, [images, searchQuery, selectedCategory, selectedTags])

  // 清除所有过滤器
  const handleClearFilters = () => {
    setSearchQuery('')
    setSelectedCategory(null)
    setSelectedTags([])
  }

  // 视图切换按钮组件
  const ViewToggle = () => {
    const views = [
      { key: 'timeline' as ViewType, label: 'Timeline', icon: Clock, description: 'Chronological view' },
      { key: 'albums' as ViewType, label: 'Albums', icon: Folder, description: 'Organized collections' },
      { key: 'grid' as ViewType, label: 'Grid', icon: LayoutGrid, description: 'All photos' }
    ]

    return (
      <FadeInUp>
        <div className="flex items-center justify-center">
          <div 
            className="inline-flex rounded-2xl p-1 backdrop-blur-sm border"
            style={{
              background: galleryTheme.gradients.card,
              borderColor: galleryTheme.primary.main + '30'
            }}
          >
            {views.map((view) => {
              const Icon = view.icon
              const isActive = activeView === view.key
              
              return (
                <button
                  key={view.key}
                  onClick={() => setActiveView(view.key)}
                  className={cn(
                    "relative flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 font-medium",
                    isActive
                      ? "text-white shadow-lg transform scale-105"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                  )}
                  style={isActive ? {
                    background: galleryTheme.gradients.primary,
                    boxShadow: galleryTheme.shadows.glow
                  } : {}}
                >
                  {isActive && (
                    <div 
                      className="absolute inset-0 rounded-xl opacity-20 animate-pulse"
                      style={{ background: galleryTheme.gradients.glow }}
                    />
                  )}
                  
                  <Icon className="w-5 h-5 relative z-10" />
                  <span className="relative z-10">{view.label}</span>
                  
                  {isActive && (
                    <div className="absolute -inset-1 rounded-xl opacity-30 blur-sm"
                         style={{ background: galleryTheme.gradients.primary }} />
                  )}
                </button>
              )
            })}
          </div>
        </div>
      </FadeInUp>
    )
  }

  // 统计信息组件
  const StatsBar = () => {
    const totalImages = images.length
    const filteredCount = filteredImages.length
    const totalAlbums = albums.length
    const totalTimelineEntries = timelineEntries.length

    return (
      <FadeInUp>
        <div 
          className="flex items-center justify-between p-4 rounded-xl backdrop-blur-sm border"
          style={{
            background: galleryTheme.gradients.subtle,
            borderColor: galleryTheme.primary.main + '20'
          }}
        >
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <ImageIcon className="w-4 h-4" style={{ color: galleryTheme.primary.main }} />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {filteredCount} of {totalImages} photos
              </span>
            </div>
            
            {totalAlbums > 0 && (
              <div className="flex items-center gap-2">
                <Folder className="w-4 h-4" style={{ color: galleryTheme.primary.main }} />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {totalAlbums} albums
                </span>
              </div>
            )}
            
            {totalTimelineEntries > 0 && (
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" style={{ color: galleryTheme.primary.main }} />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {totalTimelineEntries} memories
                </span>
              </div>
            )}
          </div>

          {(searchQuery || selectedCategory || selectedTags.length > 0) && (
            <button
              onClick={handleClearFilters}
              className="text-sm text-primary hover:text-primary/80 transition-colors font-medium"
            >
              Clear filters
            </button>
          )}
        </div>
      </FadeInUp>
    )
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <FadeInUp>
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <GlowEffect intensity="normal">
              <Sparkles className="w-8 h-8" style={{ color: galleryTheme.primary.main }} />
            </GlowEffect>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              {galleryTitle}
            </h1>
            <GlowEffect intensity="normal">
              <Sparkles className="w-8 h-8" style={{ color: galleryTheme.primary.main }} />
            </GlowEffect>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            {galleryDescription}
          </p>
        </div>
      </FadeInUp>

      {/* 搜索和过滤器 */}
      <EnhancedSearch
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        categories={categories}
        selectedCategory={selectedCategory}
        onCategorySelect={setSelectedCategory}
        selectedTags={selectedTags}
        onTagSelect={setSelectedTags}
        availableTags={availableTags}
        onClearFilters={handleClearFilters}
        showFilters={showFilters}
        onToggleFilters={() => setShowFilters(!showFilters)}
      />

      {/* 视图切换 */}
      <ViewToggle />

      {/* 统计信息 */}
      <StatsBar />

      {/* 内容区域 */}
      <div className="min-h-[400px]">
        {activeView === 'timeline' && (
          <SlideIn direction="up">
            <EnhancedTimeline
              entries={timelineEntries}
              highlightedEntryId={highlightedEntryId}
            />
          </SlideIn>
        )}

        {activeView === 'albums' && (
          <SlideIn direction="up">
            <EnhancedAlbums
              albums={convertedAlbums}
            />
          </SlideIn>
        )}

        {activeView === 'grid' && (
          <SlideIn direction="up">
            <EnhancedGrid
              images={filteredImages}
              loading={loading}
              searchQuery={searchQuery}
              selectedCategory={selectedCategory}
              selectedTags={selectedTags}
              layout="masonry"
            />
          </SlideIn>
        )}
      </div>
    </div>
  )
}
