"use client"

import Link from 'next/link'
import { useEffect, useState } from 'react'

import { ContainerInner, ContainerOuter } from '@/components/layout/Container'
import { getFooterNavigation } from '@/lib/api'
import { ThemeToggle } from '@/components/shared/ThemeToggle'
import { name } from '@/config/infoConfig'
import ClientSocialLinksWrapper from '@/components/home/<USER>'



function NavLink({
  href,
  children,
}: {
  href: string
  children: React.ReactNode
}) {
  return (
    <Link
      href={href}
      className="transition hover:text-primary"
    >
      {children}
    </Link>
  )
}

export function Footer() {
  const [footerItems, setFooterItems] = useState<Array<{name: string, href: string, icon?: string}>>([
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Projects', href: '/projects' },
    { name: 'Blogs', href: '/blogs' },
    { name: 'Gallery', href: '/gallery' }
  ]);

  useEffect(() => {
    const loadFooterNavigation = () => {
      getFooterNavigation()
        .then((navigationData) => {
          const items = navigationData.map(item => ({
            name: item.name,
            href: item.href,
            icon: item.icon
          }));
          setFooterItems(items);
        })
        .catch((error) => {
          // Failed to load footer navigation (silently handled in production)
          // 保持默认值，不需要额外处理
        });
    };

    loadFooterNavigation();
  }, []);

  return (
    <footer className="mt-16 flex-none">
      <ContainerOuter>
        <div className="border-t border-muted pb-16 pt-10">
          <ContainerInner>
            <div className="flex flex-col items-center justify-between gap-6 sm:flex-row sm:items-start">
              <div className="flex flex-col gap-4">
                <div className="flex flex-wrap justify-center gap-x-6 gap-y-1 text-sm font-medium">
                  {footerItems.map((item) => (
                    <NavLink key={item.name} href={item.href}>{item.name}</NavLink>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground text-center">
                  Frontend template from <a href="https://github.com/iAmCorey/coreychiu-portfolio-template" target="_blank" rel="noopener noreferrer" className="underline hover:text-primary">coreychiu-portfolio-template</a><br />
                  Full-stack personal website based on Next.js + MySQL + FastAPI
                </p>
              </div>
              <div className='flex flex-col justify-center items-start'>
                <div className='flex flex-row justify-end items-center gap-2'>
                  <p className="text-sm text-muted-foreground">
                    &copy; {new Date().getFullYear()} {name}. All rights reserved.
                  </p>
                  <Link
                    href="/version-history"
                    className="text-xs text-muted-foreground hover:text-primary transition-colors duration-200 underline"
                    title="查看网站版本历史"
                  >
                    Version
                  </Link>
                  <ThemeToggle />
                </div>
                <ClientSocialLinksWrapper className='mt-0'/>
              </div>
            </div>
          </ContainerInner>
        </div>
      </ContainerOuter>
    </footer>
  )
}
